export const DBErrorMessage = {
  UNIQUE_VIOLATION: 'duplicate key value violates unique constraint',
};

export const DBErrorCode = {
  UNIQUE_VIOLATION: '23505',
  CHECK_VIOLATION: '23514',
  NOT_NULL_VIOLATION: '23502',
  FOREIGN_KEY_VIOLATION: '23503',
};

export const DBConstraints = {};

// ** IMPORTANT: PostgreSQL's Max Identifier Length Is 63 Bytes **
// --> Length of index's name must less 63 characters
export const DBIndexes = {
  IDX_USER_EMAIL: 'idx_user_email',
  IDX_AUDIT_CHECKLIST_CODE_COMPANYID: 'idx_audit_checklist_code_companyId',
  IDX_CHECKLIST_QUESTION_ORDER: 'idx_checklist_question_order',
  IDX_GROUP_CODE: 'idx_group_code',
  IDX_GROUP_NAME: 'idx_group_name',
  IDX_COMPANY_CODE: 'idx_company_code',
  IDX_COMPANY_NAME: 'idx_company_name',
  IDX_COMPANY_IMO: 'idx_company_imo',
  IDX_AUDIT_TYPE_CODE_COMPANYID: 'idx_audit_type_code_companyId',
  IDX_AUDIT_TYPE_NAME_COMPANYID: 'idx_audit_type_name_companyId',
  IDX_VESSEL_IMO_NUMBER: 'idx_vessel_imo_number',
  IDX_VESSEL_CODE_COMPANYID: 'idx_vessel_code_companyId',
  IDX_VESSEL_NAME_COMPANYID: 'idx_vessel_name_companyId',
  IDX_VESSEL_TYPE_CODE_COMPANYID: 'idx_vessel_type_code_companyId',
  IDX_VESSEL_TYPE_NAME_COMPANYID: 'idx_vessel_type_name_companyId',
  IDX_FLEET_CODE_COMPANYID: 'idx_fleet_code_companyId',
  IDX_FLEET_NAME_COMPANYID: 'idx_fleet_name_companyId',
  IDX_SHORE_RANK_CODE_COMPANYID: 'idx_shore_rank_code_companyId',
  IDX_SHORE_RANK_NAME_COMPANYID: 'idx_shore_rank_name_companyId',
  IDX_RANK_CODE_COMPANYID: 'idx_rank_code_companyId',
  IDX_RANK_NAME_COMPANYID: 'idx_rank_name_companyId',
  IDX_DEPARTMENT_CODE_COMPANYID: 'idx_department_code_companyId',
  IDX_DEPARTMENT_NAME_COMPANYID: 'idx_department_name_companyId',
  IDX_SHORE_DEPARTMENT_CODE_COMPANYID: 'idx_shore_department_code_companyId',
  IDX_SHORE_DEPARTMENT_NAME_COMPANYID: 'idx_shore_department_name_companyId',
  IDX_CHARTER_OWNER_CODE_COMPANYID: 'idx_charter_owner_code_companyId',
  IDX_CHARTER_OWNER_NAME_COMPANYID: 'idx_charter_owner_name_companyId',
  IDX_PORT_MASTER_CODE: 'idx_port_master_code',
  IDX_TOPIC_CODE_COMPANY_ID: 'idx_topic_code_companyId',
  IDX_TOPIC_NAME_COMPANY_ID: 'idx_topic_name_companyId',
  IDX_PORT_CODE_COMPANY_ID: 'idx_port_code_companyId',
  IDX_PORT_CODE_COMPANY_ID_NULL: 'idx_port_code_companyId_null',
  IDX_SHIP_DEPARTMENT_CODE_COMPANYID: 'idx_ship_department_code_companyId',
  IDX_SHIP_DEPARTMENT_NAME_COMPANYID: 'idx_ship_department_name_companyId',
  IDX_SHIP_RANK_CODE_COMPANYID: 'idx_ship_rank_code_companyId',
  IDX_SHIP_RANK_NAME_COMPANYID: 'idx_ship_rank_name_companyId',
  IDX_CDI_CODE_COMPANYID: 'idx_CDI_code_companyId',
  IDX_CDI_NAME_COMPANYID: 'idx_CDI_name_companyId',
  IDX_CATEGORY_CODE_COMPANYID: 'idx_category_code_companyId',
  IDX_CATEGORY_NAME_COMPANYID: 'idx_category_name_companyId',
  IDX_PMS_NAME_COMPANYID: 'idx_pms_name_companyId',
  IDX_PMS_CODE_COMPANYID: 'idx_pms_code_companyId',
  IDX_PMS_TASKCODE_COMPANYID: 'idx_pms_task_code_companyId',
  IDX_PMS_TASKNAME_COMPANYID: 'idx_pms_task_name_companyId',
  IDX_DMS_CODE_COMPANYID: 'idx_dms_code_companyId',
  IDX_DMS_NAME_COMPANYID: 'idx_dms_name_companyId',
  IDX_SMS_CODE_COMPANYID: 'idx_sms_code_companyId',
  IDX_SMS_NAME_COMPANYID: 'idx_sms_name_companyId',
  IDX_SDR_CODE_COMPANYID: 'idx_sdr_code_companyId',
  IDX_SDR_NAME_COMPANYID: 'idx_sdr_name_companyId',
  IDX_VIQ_SYSTEM_VERSION_NO: 'idx_viq_system_version_no',
  IDX_VIQ_UDF_VERSION_NO_COMPANYID: 'idx_viq_udf_version_no_companyId',
  IDX_LOCATION_CODE_COMPANYID: 'idx_location_code_companyId',
  IDX_LOCATION_NAME_COMPANYID: 'idx_location_name_companyId',
  IDX_LOCATION_ACRONYM_COMPANYID: 'idx_location_acronym_companyId',
  IDX_MAINVIQ_VIQID_MAINCATEGORYNO: 'idx_mainViq_maincCategoryNo_viqId_companyId',
  IDX_MAINVIQ_VIQIDD_MAINCATEGORYNAME: 'idx_mainViq_mainCategoryName_viqId_companyId',
  IDX_MAINCATEGORY_CODE_COMPANYID: 'idx_mainCategory_code_companyId',
  IDX_MAINCATEGORY_NAME_COMPANYID: 'idx_mainCategory_name_companyId',
  IDX_MAINCATEGORY_ACRONYM_COMPANYID: 'idx_mainCategory_acronym_companyId',
  IDX_SECONDCATEGORY_CODE_COMPANYID: 'idx_secondCategory_code_companyId',
  IDX_SECONDCATEGORY_NAME_COMPANYID: 'idx_secondCategory_name_companyId',
  IDX_SECONDCATEGORY_ACRONYM_COMPANYID: 'idx_secondCategory_acronym_companyId',
  IDX_THIRDCATEGORY_CODE_COMPANYID: 'idx_thirdCategory_code_companyId',
  IDX_THIRDCATEGORY_NAME_COMPANYID: 'idx_thirdCategory_name_companyId',
  IDX_AUDITOR_NO_PLANNING_REQUEST_ID: 'idx_auditor_no_planning_request_id',
  IDX_VIQSUB_MAINID_SUBNO_PARENTID_NULL: 'idx_viqSub_mainId_subNo_parentId_null',
  IDX_VIQSUB_MAINID_SUBNO: 'idx_viqSub_mainId_subNo',
  IDX_VIQSUB_MAINID_SUBNAME_PARENTID_NULL: 'idx_viqSub_mainId_subName_parentId_null',
  IDX_VIQSUB_MAINID_SUBNAME: 'idx_viqSub_mainId_subName',
  IDX_AUTHORITY_MASTER_CODE_COMPANYID: 'idx_authority_master_code_companyId',
  IDX_AUTHORITY_MASTER_NAME_COMPANYID: 'idx_authority_master_name_companyId',
  IDX_NATUREFINDING_CODE_COMPANYID: 'idx_nature_finding_code_companyId',
  IDX_NATUREFINDING_NAME_COMPANYID: 'idx_nature_finding_name_companyId',
  IDX_PSC_ACTION_MASTER_CODE_COMPANYID: 'idx_psc_action_master_code_companyId',
  IDX_PSC_ACTION_MASTER_NAME_COMPANYID: 'idx_psc_action_master_name_companyId',
  IDX_PSC_DEFICIENCY_MASTER_CODE_COMPANYID: 'idx_psc_deficiency_master_code_companyId',
  IDX_AUDIT_TIME_TABLE_PLANNING_ID: 'idx_audit_time_table_planningRequestId',
  IDX_AUDIT_TIME_TABLE_CALENDAR_TIME_TABLE_ID_AUDITORID_DATE:
    'idx audit_time_table_calendar_timeTableId_auditorId_date',
  IDX_FILL_CHKLIST_WEBID_COMPANYID: 'idx_fillAuditCheckList_webId_companyId',
  IDX_FILL_CHKLIST_APPID_COMPANYID: 'idx_fillAuditCheckList_appId_companyId',
  IDX_AUDIT_WORKSPACE_PLANNING_ID: 'idx_auditWorkspace_planningRequestId',
  IDX_FILLCKLIST_FILLCHKLISTID_QUESTID: 'idx_fillChecklist_fillChecklistId_questionIs',
  IDX_FILLSACKLIST_FILLSACHKLISTID_QUESTID:
    'idx_fillSAChecklist_fillSAChecklistId_elementMasterIds',
  IDX_INSPECTION_MAPPING_AUDITTYPE: 'idx_inspection_mapping_id_auditType',
  IDX_FILLCHECKLIST_WORKSPACEID_CHECKLISTID_AUDITTYPEID:
    'idx_fillChklist_workSpaceId_chklistId_auditTypeId',
  IDX_CATEGORYMAPPING_MAIN_ID_SECOND_ID_COMPANY_ID: 'idx_categoryMapping_mainId_secondId_companyId',
  IDX_TEMPLATE_MODULE_NAME_CRUSERID: 'idx_template_module_name_crUserId',
  IDX_DEVICE_CONTROL_DEVICE_ID_VERSION_COMPANYID: 'idx_deviceControls_deviceId_version_CompanyId',
  IDX_FOCUS_REQUEST_CODE_COMPANYID: 'idx_focusRequest_code_companyId',
  IDX_FOCUS_REQUEST_QUESTION_COMPANYID: 'idx_focusRequest_question_companyId',
  IDX_LABEL_CONFIG_MODULEID_NAME: 'idx_labelConfig_moduleId_name',
  IDX_LABEL_CONFIG_MODULEID_KEY_LANGUAGE: 'idx_labelConfig_moduleId_key_language',
  IDX_MODULE_CONFIG_CODE_COMPANYID: 'idx_moduleConfig_code_companyId',
  IDX_MODULE_CONFIG_NAME_COMPANYID: 'idx_moduleConfig_name_companyId',
  IDX_MODULE_LABEL_ACTION: 'idx_module_label_action',
  IDX_STANDARD_MODULE_LABEL_ACTION: 'idx_standardMaster_module_label_action',
  IDX_MODULE_CONFIG_KEY_LANGUAGE_COMPANYID: 'idx_moduleConfig_key_lang_companyId',
  IDX_STANDARDMASTER_CODE_COMPANYID: 'idx_standardMaster_code_companyId',
  IDX_ELEMENTMASTER_CODE_COMPANYID_STANDARDMASTERID:
    'idx_elementMaster_code_companyId_standardMasterId',
  IDX_INCIDENTMASTER_CODE_COMPANYID: 'idx_incidentMaster_code_companyId',
  IDX_CATEGORIZATIONMASTER_CODE_COMPANYID: 'idx_categorizationMaster_code_companyId',
  IDX_CATEGORIZATIONMASTER_NAME_COMPANYID: 'idx_categorizationMaster_name_companyId',
  IDX_VOYAGETYPE_CODE_COMPANYID: 'idx_voyageType_code_companyId',
  IDX_VOYAGETYPE_NAME_COMPANYID: 'idx_voyageType_name_companyId',
  IDX_INCIDENTMASTER_NAME_COMPANYID: 'idx_incidentMaster_name_companyId',
  IDX_SUBINCIDENT_CODE_COMPANYID: 'idx_subIncident_code_companyId',
  IDX_SUBINCIDENT_NAME_COMPANYID: 'idx_subIncident_name_companyId',
  IDX_EVENTTYPE_CODE_COMPANYID: 'idx_eventType_code_companyId',
  IDX_EVENTTYPE_NAME_COMPANYID: 'idx_eventType_name_companyId',
  IDX_TECHISSUENOTE_CODE_COMPANYID: 'idx_techIssueNote_code_companyId',
  IDX_TECHISSUENOTE_NAME_COMPANYID: 'idx_techIssueNote_name_companyId',
  IDX_INJURYMASTER_CODE_COMPANYID: 'idx_injuryMaster_code_companyId',
  IDX_INJURYMASTER_NAME_COMPANYID: 'idx_injuryMaster_name_companyId',
  IDX_INJURYBODY_CODE_COMPANYID: 'idx_injuryBody_code_companyId',
  IDX_INJURYBODY_NAME_COMPANYID: 'idx_injuryBody_name_companyId',
  ATTACHMENT_KIT_CODE_COMPANYID: 'idx_attachmentKit_code_companyId',
  ATTACHMENT_KIT_NAME_COMPANYID: 'idx_attachmentKit_name_companyId',
  MAIL_TEMPLATE_CODE_COMPANYID: 'idx_mailTemplate_code_companyId',
  MAIL_TEMPLATE_NAME_COMPANYID: 'idx_mailTemplate_name_companyId',
  MAIL_TEMPLATE_VESSELTYPEID_COMPANYID_MAILTYPEID_WORKINGTYPE_MODULE:
    'idx_mailTemplate_vtId_companyId_mtId_wt_module',
  MAIL_TEMPLATE_COMPANYID_MAILTYPEID_WORKINGTYPE_MODULE:
    'idx_mailTemplate_companyId_mailTypeId_workingType_module',
  IDX_PREVIOUS_FINDING_ITEMS_INSPECTION_TYPE_ID_IARID_TYPE:
    'idx_previous_finding_items_inspection_type_id_iar_id_type',
  IDX_TERMINAL_CODE_COMPANYID: 'idx_terminal_code_companyId',
  IDX_TERMINAL_NAME_COMPANYID: 'idx_terminal_name_companyId',
  IDX_RISKFACTOR_CODE_COMPANYID: 'idx_riskFactor_code_companyId',
  IDX_RISKFACTOR_NAME_COMPANYID: 'idx_riskFactor_name_companyId',
  IDX_TRANSFERTYPE_CODE_COMPANYID: 'idx_transferType_code_companyId',
  IDX_TRANSFERTYPE_NAME_COMPANYID: 'idx_transferType_name_companyId',
  IDX_CARGOTYPE_CODE_COMPANYID: 'idx_cargoType_code_companyId',
  IDX_CARGOTYPE_NAME_COMPANYID: 'idx_cargoType_name_companyId',
  IDX_CARGO_CODE_COMPANYID: 'idx_cargo_code_companyId',
  IDX_CARGO_NAME_COMPANYID: 'idx_cargo_name_companyId',
  IDX_VESSEL_SCREENING_EMAILREQUEST: 'idx_vesselScreening_emailRequest',
  IDX_VALUE_MANAGEMENT_CODE_COMPANYID: 'idx_valueManagement_code_companyId',
  IDX_VALUE_MANAGEMENT_NAME_COMPANYID: 'idx_valueManagement_number_companyId',
  IDX_VESSEL_OWNER_BUSINESS_CODE_COMPANYID: 'idx_vessel_owner_business_code_companyId',
  IDX_VESSEL_OWNER_BUSINESS_NAME_COMPANYID: 'idx_vessel_owner_business_name_companyId',
  IDX_PLANSDRAWINGSMASTER_CODE_COMPANYID: 'idx_plansDrawingsMaster_code_companyId',
  IDX_PLANSDRAWINGSMASTER_NAME_COMPANYID: 'idx_plansDrawingsMaster_name_companyId',
  IDX_CLASSIFICATION_SOCIETY_CODE: 'idx_classification_society_code',
  IDX_CLASSIFICATION_SOCIETY_NAME: 'idx_classification_society_name',
  IDX_CREW_GROUPING_CODE_COMPANYID: 'idx_crew_grouping_code_companyId',
  IDX_CREW_GROUPING_NAME_COMPANYID: 'idx_crew_grouping_name_companyId',
  IDX_DIVISION_CODE_COMPANYID: 'idx_division_code_companyId',
  IDX_DIVISION_NAME_COMPANYID: 'idx_division_name_companyId',
  IDX_PACKAGE_TYPE: 'idx_package_type',
  IDX_INDUSTRY_CODE: 'idx_industry_code',
  IDX_COMPANY_TYPE: 'idx_company_type',
  IDX_WIDGET_USERID_MODULE: 'idx_widget_userId_module',
  IDX_VOYAGE_STATUS_CODE_COMPANYID: 'idx_voyage_status_code_conf',
  IDX_VOYAGE_STATUS_NAME_COMPANYID: 'idx_voyage_status_name_company_conf',
  IDX_NAME_PROVIDER_CONFIG: 'idx_name_provider_config',
  IDX_DOMAIN_PROVIDER_CONFIG: 'idx_domain_provider_config',
  IDX_COMPANY_CODE_PARENTID: 'idx_company_code_parentId',
  IDX_COMPANY_NAME_PARENTID: 'idx_company_name_parentId',
  IDX_CHECKLIST_QUESTION: 'idx_checklist_question',
  IDX_FILLQUESTION_QUESTION: 'idx_fillquestion_question',
  IDX_FILLSAQUESTION_QUESTION: 'idx_fillsaquestion_question',
  IDX_QUESTION_ANSWER_OPTION: 'idx_question_answer_option',
  IDX_QUESTION_REF_CATE_DATA: 'idx_question_ref_cate_data',
  IDX_POWER_BI_MODULE_COMPANY: 'idx_power_bi_module_company',
  IDX_CVIQ_VERSION: 'idx_cviq_version',
};

export const DBChecks = {};
