-- Test script for Cause Analysis Master migration
-- Run this after the migration to verify everything works correctly

-- 1. Verify tables were created
SELECT 'Tables created successfully' as status;
SELECT table_name, table_type 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('cause_analysis_master', 'cause_main_category', 'cause_sub_category');

-- 2. Verify enum types were created
SELECT 'Enum types created successfully' as status;
SELECT typname as enum_name
FROM pg_type 
WHERE typname LIKE 'cause_%_enum'
ORDER BY typname;

-- 3. Verify indexes were created
SELECT 'Indexes created successfully' as status;
SELECT indexname, tablename
FROM pg_indexes 
WHERE tablename LIKE 'cause_%'
ORDER BY tablename, indexname;

-- 4. Verify foreign key constraints
SELECT 'Foreign key constraints created successfully' as status;
SELECT 
    conname as constraint_name,
    conrelid::regclass as table_name,
    confrelid::regclass as referenced_table
FROM pg_constraint 
WHERE contype = 'f' 
AND conrelid::regclass::text LIKE 'cause_%'
ORDER BY conrelid::regclass;

-- 5. Test enum values
SELECT 'Testing enum values' as status;

-- Test cause type enum
SELECT unnest(enum_range(NULL::cause_analysis_master_causetype_enum)) as cause_types;

-- Test risk enum
SELECT unnest(enum_range(NULL::cause_main_category_potentialrisk_enum)) as risk_levels;

-- Test status enums
SELECT unnest(enum_range(NULL::cause_analysis_master_status_enum)) as master_status;
SELECT unnest(enum_range(NULL::cause_main_category_status_enum)) as category_status;

-- 6. Test table structure
SELECT 'Verifying table structures' as status;

-- cause_analysis_master columns
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'cause_analysis_master'
ORDER BY ordinal_position;

-- cause_main_category columns
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'cause_main_category'
ORDER BY ordinal_position;

-- cause_sub_category columns
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'cause_sub_category'
ORDER BY ordinal_position;

-- 7. Test unique constraints
SELECT 'Verifying unique constraints' as status;
SELECT 
    tc.constraint_name,
    tc.table_name,
    kcu.column_name
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu 
    ON tc.constraint_name = kcu.constraint_name
WHERE tc.constraint_type = 'UNIQUE'
AND tc.table_name LIKE 'cause_%'
ORDER BY tc.table_name, tc.constraint_name;

-- 8. Sample data insertion test (requires existing company and user)
-- Note: Replace with actual company and user IDs from your database

/*
-- Uncomment and modify these queries with real IDs to test data insertion

-- Get a sample company ID
SELECT id as company_id FROM company LIMIT 1;

-- Get a sample user ID  
SELECT id as user_id FROM "user" LIMIT 1;

-- Test insertion (replace UUIDs with actual values)
INSERT INTO cause_analysis_master (
    id, causeType, udfVersionNo, refNo, status, companyId, createdUserId
) VALUES (
    gen_random_uuid(),
    'Basic',
    'TEST_V1.0',
    'TESTREF001',
    'active',
    'YOUR_COMPANY_ID_HERE',  -- Replace with actual company ID
    'YOUR_USER_ID_HERE'      -- Replace with actual user ID
);

-- Test main category insertion
INSERT INTO cause_main_category (
    id, mainCategoryNo, mainCategoryName, potentialRisk, 
    causeAnalysisMasterId, companyId
) VALUES (
    gen_random_uuid(),
    '1',
    'Test Main Category',
    'High',
    (SELECT id FROM cause_analysis_master WHERE udfVersionNo = 'TEST_V1.0'),
    'YOUR_COMPANY_ID_HERE'   -- Replace with actual company ID
);

-- Test sub category insertion (level 1)
INSERT INTO cause_sub_category (
    id, subCategoryName, subRefNo, level, 
    causeMainCategoryId, companyId
) VALUES (
    gen_random_uuid(),
    'Test Sub Category Level 1',
    '1.1',
    1,
    (SELECT id FROM cause_main_category WHERE mainCategoryName = 'Test Main Category'),
    'YOUR_COMPANY_ID_HERE'   -- Replace with actual company ID
);

-- Test sub category insertion (level 2)
INSERT INTO cause_sub_category (
    id, subCategoryName, subRefNo, level, parentId,
    causeMainCategoryId, companyId
) VALUES (
    gen_random_uuid(),
    'Test Sub Category Level 2',
    '1.1.1',
    2,
    (SELECT id FROM cause_sub_category WHERE subRefNo = '1.1'),
    (SELECT id FROM cause_main_category WHERE mainCategoryName = 'Test Main Category'),
    'YOUR_COMPANY_ID_HERE'   -- Replace with actual company ID
);

-- Verify the hierarchy
SELECT 
    cam.causeType,
    cam.udfVersionNo,
    cmc.mainCategoryName,
    csc1.subCategoryName as level1_subcategory,
    csc2.subCategoryName as level2_subcategory
FROM cause_analysis_master cam
LEFT JOIN cause_main_category cmc ON cam.id = cmc.causeAnalysisMasterId
LEFT JOIN cause_sub_category csc1 ON cmc.id = csc1.causeMainCategoryId AND csc1.level = 1
LEFT JOIN cause_sub_category csc2 ON csc1.id = csc2.parentId AND csc2.level = 2
WHERE cam.udfVersionNo = 'TEST_V1.0';

-- Clean up test data
DELETE FROM cause_analysis_master WHERE udfVersionNo = 'TEST_V1.0';
*/

SELECT 'Migration verification completed successfully!' as final_status;
