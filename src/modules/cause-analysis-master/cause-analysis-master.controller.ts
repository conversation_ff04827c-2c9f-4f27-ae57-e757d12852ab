import {
  Body,
  Controller,
  Delete,
  Get,
  HttpStatus,
  Param,
  ParseU<PERSON><PERSON>ipe,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { I18n, I18nContext } from 'nestjs-i18n';
import {
  AuthGuard,
  RequiredPermissions,
  Roles,
  RoleScope,
  RolesGuard,
  TokenDecorator,
  TokenPayloadModel,
} from 'svm-nest-lib-v3';
import {
  ActionEnum,
  FeatureEnum,
  SubFeatureEnum,
} from '../../commons/enums';
import { CreateCauseAnalysisMasterDto, ListCauseAnalysisMasterDto } from './dto';
import { CauseAnalysisMasterService } from './cause-analysis-master.service';

@ApiTags('Cause Analysis Master')
@Controller('cause-analysis-master')
@ApiBearerAuth()
@UseGuards(AuthGuard, RolesGuard)
export class CauseAnalysisMasterController {
  constructor(private readonly causeAnalysisMasterService: CauseAnalysisMasterService) {}

  @ApiOperation({ summary: 'Create new cause analysis master' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Create cause analysis master successfully',
  })
  @ApiBody({ type: CreateCauseAnalysisMasterDto })
  @Roles(RoleScope.ADMIN)
  @RequiredPermissions({
    feature: FeatureEnum.QUALITY_ASSURANCE_INCIDENTS + '::' + SubFeatureEnum.CAUSE_ANALYSIS_MASTER,
    action: ActionEnum.CREATE,
  })
  @Post()
  async createCauseAnalysisMaster(
    @TokenDecorator() token: TokenPayloadModel,
    @I18n() i18n: I18nContext,
    @Body() body: CreateCauseAnalysisMasterDto,
  ) {
    const id = await this.causeAnalysisMasterService.createCauseAnalysisMaster(body, token);
    return {
      message: await i18n.t('common.CREATE_SUCCESS'),
      id,
    };
  }

  @ApiOperation({ summary: 'Get list of cause analysis masters' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Get list cause analysis master successfully',
  })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature: FeatureEnum.QUALITY_ASSURANCE_INCIDENTS + '::' + SubFeatureEnum.CAUSE_ANALYSIS_MASTER,
    action: ActionEnum.VIEW,
  })
  @Get()
  async getListCauseAnalysisMaster(
    @TokenDecorator() token: TokenPayloadModel,
    @Query() query: ListCauseAnalysisMasterDto,
  ) {
    return this.causeAnalysisMasterService.getListCauseAnalysisMaster(query, token);
  }

  @ApiOperation({ summary: 'Get cause analysis master detail' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Get cause analysis master detail successfully',
  })
  @ApiParam({
    name: 'id',
    description: 'Cause analysis master id',
    type: 'string',
  })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature: FeatureEnum.QUALITY_ASSURANCE_INCIDENTS + '::' + SubFeatureEnum.CAUSE_ANALYSIS_MASTER,
    action: ActionEnum.VIEW,
  })
  @Get('/:id')
  async getDetailCauseAnalysisMaster(
    @Param('id', ParseUUIDPipe) id: string,
    @TokenDecorator() token: TokenPayloadModel,
  ) {
    return this.causeAnalysisMasterService.getDetailCauseAnalysisMaster(id, token);
  }

  @ApiOperation({ summary: 'Delete cause analysis master' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Delete cause analysis master successfully',
  })
  @ApiParam({
    name: 'id',
    description: 'Cause analysis master id',
    type: 'string',
  })
  @Roles(RoleScope.ADMIN)
  @RequiredPermissions({
    feature: FeatureEnum.QUALITY_ASSURANCE_INCIDENTS + '::' + SubFeatureEnum.CAUSE_ANALYSIS_MASTER,
    action: ActionEnum.DELETE,
  })
  @Delete('/:id')
  async deleteCauseAnalysisMaster(
    @Param('id', ParseUUIDPipe) id: string,
    @TokenDecorator() token: TokenPayloadModel,
    @I18n() i18n: I18nContext,
  ) {
    await this.causeAnalysisMasterService.deleteCauseAnalysisMaster(id, token);
    return {
      message: await i18n.t('common.DELETE_SUCCESS'),
    };
  }
}
