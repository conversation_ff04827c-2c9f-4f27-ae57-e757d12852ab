# Cause Analysis Master Migration

## Overview
This migration creates the database tables and relationships for the Cause Analysis Master module, implementing a 3-level hierarchical structure for cause analysis data.

## Migration File
- **File**: `src/migrations/1749027489000-create-cause-analysis-master-tables.ts`
- **Timestamp**: 1749027489000
- **Name**: createCauseAnalysisMasterTables1749027489000

## Tables Created

### 1. cause_analysis_master
Main entity table for cause analysis masters.

**Columns:**
- `id` (uuid, PK) - Primary key
- `deleted` (boolean) - Soft delete flag
- `createdAt` (timestamp) - Creation timestamp
- `updatedAt` (timestamp) - Update timestamp
- `causeType` (enum) - Type of cause analysis
- `udfVersionNo` (varchar) - User-defined version number
- `refNo` (varchar) - System-generated reference number
- `status` (enum) - Active/Inactive status
- `timezone` (varchar) - Timezone for the record
- `companyId` (uuid, FK) - Reference to company
- `createdUserId` (uuid, FK) - Reference to creating user
- `updatedUserId` (uuid, FK) - Reference to updating user

**Enums:**
- `causeType`: 'Basic', 'Immediate', 'Control Action Needs', 'Type of Loss'
- `status`: 'active', 'inactive'

### 2. cause_main_category
Main categories within a cause analysis master.

**Columns:**
- `id` (uuid, PK) - Primary key
- `deleted` (boolean) - Soft delete flag
- `createdAt` (timestamp) - Creation timestamp
- `updatedAt` (timestamp) - Update timestamp
- `mainCategoryNo` (varchar) - Category number for ordering
- `mainCategoryName` (varchar) - Category name
- `potentialRisk` (enum) - Risk level assessment
- `status` (enum) - Published/Draft status
- `causeAnalysisMasterId` (uuid, FK) - Reference to parent master
- `companyId` (uuid, FK) - Reference to company

**Enums:**
- `potentialRisk`: 'High', 'Medium', 'Low', 'Negligible'
- `status`: 'published', 'draft'

### 3. cause_sub_category
Sub-categories with self-referencing for 2-level hierarchy.

**Columns:**
- `id` (uuid, PK) - Primary key
- `deleted` (boolean) - Soft delete flag
- `createdAt` (timestamp) - Creation timestamp
- `updatedAt` (timestamp) - Update timestamp
- `subCategoryName` (varchar) - Sub-category name
- `status` (enum) - Published/Draft status
- `parentId` (uuid, FK) - Self-reference for second sub-category
- `subRefNo` (varchar) - Reference number for ordering
- `causeMainCategoryId` (uuid, FK) - Reference to main category
- `companyId` (uuid, FK) - Reference to company
- `level` (integer) - Level indicator (1 or 2)

**Enums:**
- `status`: 'published', 'draft'

## Relationships

### Foreign Key Constraints
1. `cause_analysis_master.companyId` → `company.id` (CASCADE DELETE)
2. `cause_analysis_master.createdUserId` → `user.id` (NO ACTION)
3. `cause_analysis_master.updatedUserId` → `user.id` (NO ACTION)
4. `cause_main_category.causeAnalysisMasterId` → `cause_analysis_master.id` (CASCADE DELETE)
5. `cause_main_category.companyId` → `company.id` (CASCADE DELETE)
6. `cause_sub_category.causeMainCategoryId` → `cause_main_category.id` (CASCADE DELETE)
7. `cause_sub_category.parentId` → `cause_sub_category.id` (CASCADE DELETE)

### Hierarchy Structure
```
cause_analysis_master
├── cause_main_category (1:N)
    ├── cause_sub_category (level 1) (1:N)
        └── cause_sub_category (level 2) (1:N)
```

## Indexes Created

### Unique Indexes (Business Logic Constraints)
1. `idx_cause_analysis_master_udf_version_no_companyId` - Unique version per company
2. `idx_main_cause_analysis_master_id_main_category_no` - Unique category number per master
3. `idx_main_cause_analysis_master_id_main_category_name` - Unique category name per master
4. `idx_cause_sub_main_id_sub_no` - Unique sub ref number for level 1 categories
5. `idx_cause_sub_main_id_sub_no_parent` - Unique sub ref number for level 2 categories

### Performance Indexes
1. `idx_cause_analysis_master_company_id` - Company filtering
2. `idx_cause_analysis_master_cause_type` - Cause type filtering
3. `idx_cause_analysis_master_status` - Status filtering
4. `idx_cause_main_category_master_id` - Main category lookups
5. `idx_cause_main_category_company_id` - Company filtering
6. `idx_cause_sub_category_main_id` - Sub category lookups
7. `idx_cause_sub_category_parent_id` - Hierarchy navigation
8. `idx_cause_sub_category_company_id` - Company filtering
9. `idx_cause_sub_category_level` - Level-based queries

## Business Rules Enforced

### 1. Active Record Constraints
- Only one active record per `causeType` per company (except 'Type of Loss')
- 'Type of Loss' can have multiple active records
- Implemented in application logic, not database constraints

### 2. Hierarchy Constraints
- Level 1 sub-categories: `parentId` IS NULL, `level` = 1
- Level 2 sub-categories: `parentId` IS NOT NULL, `level` = 2
- Maximum 2 levels of sub-categories

### 3. Unique Constraints
- Version numbers must be unique per company
- Category numbers and names must be unique within each master
- Sub-category reference numbers must be unique within each level

## Running the Migration

### Prerequisites
1. Ensure database connection is configured
2. Backup your database before running migrations
3. Verify that `company` and `user` tables exist

### Commands

#### Run Migration
```bash
# Development
npm run migration:run

# Production
npm run migration:run:prod
```

#### Revert Migration (if needed)
```bash
# Development
npm run migration:revert

# Production
npm run migration:revert:prod
```

#### Generate New Migration (if changes needed)
```bash
npm run migration:generate -- -n update-cause-analysis-master
```

### Verification Queries

After running the migration, verify the tables were created correctly:

```sql
-- Check tables exist
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name LIKE 'cause_%';

-- Check enum types
SELECT typname FROM pg_type 
WHERE typname LIKE 'cause_%_enum';

-- Check indexes
SELECT indexname FROM pg_indexes 
WHERE tablename LIKE 'cause_%';

-- Check foreign key constraints
SELECT conname, conrelid::regclass, confrelid::regclass 
FROM pg_constraint 
WHERE contype = 'f' 
AND conrelid::regclass::text LIKE 'cause_%';
```

## Rollback Plan

If issues occur after migration:

1. **Immediate Rollback**: Run `npm run migration:revert`
2. **Data Recovery**: Restore from backup if data corruption occurs
3. **Partial Rollback**: Manually drop specific constraints/indexes if needed

## Post-Migration Steps

1. **Test API Endpoints**: Verify all CRUD operations work
2. **Run Unit Tests**: Execute cause-analysis-master test suite
3. **Performance Testing**: Test with sample hierarchical data
4. **Data Validation**: Ensure business rules are enforced

## Troubleshooting

### Common Issues

1. **Foreign Key Violations**: Ensure `company` and `user` tables exist
2. **Enum Type Conflicts**: Check for existing enum types with same names
3. **Index Name Conflicts**: Verify index names don't conflict with existing ones
4. **Permission Issues**: Ensure database user has CREATE privileges

### Error Resolution

```sql
-- If enum already exists
DROP TYPE IF EXISTS cause_analysis_master_causetype_enum CASCADE;

-- If foreign key constraint fails
SELECT * FROM company LIMIT 1; -- Verify company table exists
SELECT * FROM "user" LIMIT 1;  -- Verify user table exists

-- If index creation fails
DROP INDEX IF EXISTS idx_cause_analysis_master_udf_version_no_companyId;
```

## Contact

For migration issues or questions, contact the development team or refer to the project documentation.
