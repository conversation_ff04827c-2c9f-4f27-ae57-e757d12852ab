# Updated cURL Examples for Cause Analysis Master

## Important Changes
The `potentialRisk` field has been changed from an enum to a foreign key relationship with the `priority_master` table. You now need to use `potentialRiskId` with the UUID of the priority master record.

## Get Priority Master IDs First

Before creating cause analysis masters, you need to get the available priority master IDs:

```bash
curl -X GET "http://localhost:3000/priority-master" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

**Expected Response:**
```json
[
  {"id": "uuid-for-high", "risk": "High", "order": 1},
  {"id": "uuid-for-medium", "risk": "Medium", "order": 2},
  {"id": "uuid-for-low", "risk": "Low", "order": 3},
  {"id": "uuid-for-negligible", "risk": "Negligible", "order": 4}
]
```

## Updated Create Examples

### 1. Create Cause Analysis Master (Updated)

```bash
curl -X POST "http://localhost:3000/cause-analysis-master" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "causeType": "Basic",
    "status": "active",
    "udfVersionNo": "BASIC_V1.0",
    "timezone": "Asia/Ho_Chi_Minh",
    "causeMainCategories": [
      {
        "mainCategoryNo": "1",
        "mainCategoryName": "Human Error",
        "potentialRiskId": "uuid-for-high-risk",
        "causeSubCategories": [
          {
            "subCategoryName": "Training Deficiency",
            "subRefNo": "1.1",
            "level": 1,
            "children": [
              {
                "subCategoryName": "Inadequate Initial Training",
                "subRefNo": "1.1.1",
                "level": 2
              },
              {
                "subCategoryName": "Lack of Refresher Training",
                "subRefNo": "1.1.2",
                "level": 2
              }
            ]
          },
          {
            "subCategoryName": "Communication Failure",
            "subRefNo": "1.2",
            "level": 1,
            "children": [
              {
                "subCategoryName": "Language Barrier",
                "subRefNo": "1.2.1",
                "level": 2
              }
            ]
          }
        ]
      },
      {
        "mainCategoryNo": "2",
        "mainCategoryName": "Equipment Failure",
        "potentialRiskId": "uuid-for-medium-risk",
        "causeSubCategories": [
          {
            "subCategoryName": "Mechanical Failure",
            "subRefNo": "2.1",
            "level": 1
          }
        ]
      }
    ]
  }'
```

### 2. Create Different Risk Levels

#### High Risk Category
```bash
curl -X POST "http://localhost:3000/cause-analysis-master" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "causeType": "Immediate",
    "status": "active",
    "udfVersionNo": "IMMEDIATE_V1.0",
    "causeMainCategories": [
      {
        "mainCategoryNo": "1",
        "mainCategoryName": "Critical Safety Failure",
        "potentialRiskId": "uuid-for-high-risk"
      }
    ]
  }'
```

#### Medium Risk Category
```bash
curl -X POST "http://localhost:3000/cause-analysis-master" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "causeType": "Control Action Needs",
    "status": "active",
    "udfVersionNo": "CONTROL_V1.0",
    "causeMainCategories": [
      {
        "mainCategoryNo": "1",
        "mainCategoryName": "Process Improvement",
        "potentialRiskId": "uuid-for-medium-risk"
      }
    ]
  }'
```

#### Low Risk Category
```bash
curl -X POST "http://localhost:3000/cause-analysis-master" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "causeType": "Type of Loss",
    "status": "active",
    "udfVersionNo": "LOSS_MINOR_V1.0",
    "causeMainCategories": [
      {
        "mainCategoryNo": "1",
        "mainCategoryName": "Minor Financial Impact",
        "potentialRiskId": "uuid-for-low-risk"
      }
    ]
  }'
```

#### Negligible Risk Category
```bash
curl -X POST "http://localhost:3000/cause-analysis-master" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "causeType": "Type of Loss",
    "status": "active",
    "udfVersionNo": "LOSS_NEGLIGIBLE_V1.0",
    "causeMainCategories": [
      {
        "mainCategoryNo": "1",
        "mainCategoryName": "Documentation Issues",
        "potentialRiskId": "uuid-for-negligible-risk"
      }
    ]
  }'
```

## Expected Response Structure

### Detail Response (with Priority Master relationship)
```json
{
  "id": "cause-analysis-master-uuid",
  "causeType": "Basic",
  "udfVersionNo": "BASIC_V1.0",
  "refNo": "CAUSESVMSG202400001",
  "status": "active",
  "causeMainCategories": [
    {
      "id": "main-category-uuid",
      "mainCategoryNo": "1",
      "mainCategoryName": "Human Error",
      "potentialRiskId": "uuid-for-high-risk",
      "potentialRisk": {
        "id": "uuid-for-high-risk",
        "risk": "High",
        "order": 1,
        "value": null,
        "description": null
      },
      "causeSubCategories": [
        {
          "id": "sub-category-uuid",
          "subCategoryName": "Training Deficiency",
          "subRefNo": "1.1",
          "level": 1,
          "children": [
            {
              "id": "second-sub-category-uuid",
              "subCategoryName": "Inadequate Initial Training",
              "subRefNo": "1.1.1",
              "level": 2
            }
          ]
        }
      ]
    }
  ]
}
```

## Helper Script to Get Priority Master IDs

You can use this script to get the priority master IDs and store them in variables:

```bash
#!/bin/bash

# Get priority master data
PRIORITY_DATA=$(curl -s -X GET "http://localhost:3000/priority-master" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN")

# Extract UUIDs (requires jq)
HIGH_RISK_ID=$(echo $PRIORITY_DATA | jq -r '.[] | select(.risk=="High") | .id')
MEDIUM_RISK_ID=$(echo $PRIORITY_DATA | jq -r '.[] | select(.risk=="Medium") | .id')
LOW_RISK_ID=$(echo $PRIORITY_DATA | jq -r '.[] | select(.risk=="Low") | .id')
NEGLIGIBLE_RISK_ID=$(echo $PRIORITY_DATA | jq -r '.[] | select(.risk=="Negligible") | .id')

echo "High Risk ID: $HIGH_RISK_ID"
echo "Medium Risk ID: $MEDIUM_RISK_ID"
echo "Low Risk ID: $LOW_RISK_ID"
echo "Negligible Risk ID: $NEGLIGIBLE_RISK_ID"

# Now use these variables in your create requests
curl -X POST "http://localhost:3000/cause-analysis-master" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d "{
    \"causeType\": \"Basic\",
    \"status\": \"active\",
    \"udfVersionNo\": \"BASIC_V1.0\",
    \"causeMainCategories\": [
      {
        \"mainCategoryNo\": \"1\",
        \"mainCategoryName\": \"Human Error\",
        \"potentialRiskId\": \"$HIGH_RISK_ID\"
      }
    ]
  }"
```

## Validation Notes

1. **potentialRiskId is required** - You must provide a valid UUID from the priority_master table
2. **Foreign key validation** - The system will validate that the potentialRiskId exists in priority_master
3. **Risk level consistency** - The API will return the full priority master object in detail responses
4. **Backward compatibility** - Old requests using `potentialRisk` enum will fail with validation errors

## Error Examples

### Invalid potentialRiskId
```bash
curl -X POST "http://localhost:3000/cause-analysis-master" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "causeType": "Basic",
    "status": "active",
    "udfVersionNo": "TEST_V1.0",
    "causeMainCategories": [
      {
        "mainCategoryNo": "1",
        "mainCategoryName": "Test Category",
        "potentialRiskId": "invalid-uuid"
      }
    ]
  }'
```

**Expected Error Response:**
```json
{
  "statusCode": 400,
  "message": ["potentialRiskId must be a UUID"],
  "error": "Bad Request"
}
```

Replace `YOUR_JWT_TOKEN` with your actual authentication token and `uuid-for-*-risk` with the actual UUIDs from your priority_master table.
