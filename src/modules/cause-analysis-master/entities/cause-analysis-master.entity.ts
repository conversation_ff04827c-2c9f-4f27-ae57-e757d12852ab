import { CommonStatus, IdentifyEntity } from 'svm-nest-lib-v3';
import { Entity, Column, ManyToOne, OneToMany, Index } from 'typeorm';
import { DBIndexes } from '../../../commons/consts/db.const';
import { CauseTypeEnum } from '../../../commons/enums';
import { User } from '../../user/user.entity';
import { Company } from '../../company/company.entity';
import { CauseMainCategory } from './cause-main-category.entity';

@Entity()
@Index(
  DBIndexes.IDX_CAUSE_ANALYSIS_MASTER_UDF_VERSION_NO_COMPANYID,
  ['udfVersionNo', 'companyId'],
  {
    unique: true,
    where: 'deleted = false',
  },
)
export class CauseAnalysisMaster extends IdentifyEntity {
  @Column({ type: 'enum', enum: CauseTypeEnum })
  public causeType: string;

  @Column()
  public udfVersionNo: string;

  @Column()
  public refNo: string;

  @Column({ type: 'enum', enum: CommonStatus })
  status: string;

  @Column({ nullable: true })
  public timezone: string;

  @Column({ type: 'uuid' })
  public companyId: string;

  @Column({ type: 'uuid' })
  public createdUserId: string;

  @Column({ type: 'uuid', nullable: true })
  public updatedUserId?: string;

  @ManyToOne(() => Company, { onDelete: 'CASCADE' })
  company: Company;

  @ManyToOne(() => User, { onDelete: 'NO ACTION' })
  createdUser: User;

  @ManyToOne(() => User, { onDelete: 'NO ACTION' })
  updatedUser: User;

  @OneToMany(
    () => CauseMainCategory,
    (causeMainCategory) => causeMainCategory.causeAnalysisMaster,
    {
      onDelete: 'CASCADE',
    },
  )
  causeMainCategories: CauseMainCategory[];
}
