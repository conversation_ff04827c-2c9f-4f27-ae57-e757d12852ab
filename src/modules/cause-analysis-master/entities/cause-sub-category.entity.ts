import { IdentifyEntity } from 'svm-nest-lib-v3';
import { Column, Index, ManyToOne, OneToMany } from 'typeorm';
import { Entity } from 'typeorm/decorator/entity/Entity';
import { DBIndexes } from '../../../commons/consts/db.const';
import { MainSubEnum } from '../../../commons/enums';
import { CauseMainCategory } from './cause-main-category.entity';

@Entity()
@Index(DBIndexes.IDX_CAUSE_SUB_MAIN_ID_SUB_NO, ['causeMainCategoryId', 'subRefNo'], {
  unique: true,
  where: '("parentId" IS NULL AND deleted = false)',
})
@Index(
  DBIndexes.IDX_CAUSE_SUB_MAIN_ID_SUB_NO_PARENT,
  ['causeMainCategoryId', 'subRefNo', 'parentId'],
  {
    unique: true,
    where: '("parentId" IS NOT NULL AND deleted = false)',
  },
)
export class CauseSubCategory extends IdentifyEntity {
  @Column()
  public subCategoryName: string;

  @Column({ type: 'enum', enum: MainSubEnum, default: MainSubEnum.PUBLISHED })
  public status: string;

  @Column({ type: 'uuid', nullable: true })
  public parentId?: string;

  @Column({ nullable: true })
  public subRefNo: string;

  @Column({ type: 'uuid' })
  public causeMainCategoryId: string;

  @Column({ type: 'uuid' })
  public companyId: string;

  @Column({ type: 'int', default: 1 })
  public level: number; // 1 for sub category, 2 for second sub category

  @ManyToOne(() => CauseSubCategory, (causeSubCategory) => causeSubCategory.children, {
    onDelete: 'CASCADE',
  })
  parent: CauseSubCategory;

  @OneToMany(() => CauseSubCategory, (causeSubCategory) => causeSubCategory.parent, {
    onDelete: 'SET NULL',
  })
  children: CauseSubCategory[];

  @ManyToOne(() => CauseMainCategory, (causeMainCategory) => causeMainCategory.causeSubCategories, {
    onDelete: 'CASCADE',
  })
  causeMainCategory: CauseMainCategory;
}
