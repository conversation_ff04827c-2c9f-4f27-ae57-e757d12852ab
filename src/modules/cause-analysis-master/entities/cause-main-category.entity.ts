import { IdentifyEntity } from 'svm-nest-lib-v3';
import { Entity, Column, ManyToOne, OneToMany, Index } from 'typeorm';
import { DBIndexes } from '../../../commons/consts/db.const';
import { MainSubEnum, RiskTypeEnum } from '../../../commons/enums';
import { Company } from '../../company/company.entity';
import { CauseSubCategory } from './cause-sub-category.entity';
import { CauseAnalysisMaster } from './cause-analysis-master.entity';

@Entity()
@Index(DBIndexes.IDX_MAIN_CAUSE_ANALYSIS_MASTER_ID_MAIN_CATEGORY_NO, ['causeAnalysisMasterId', 'mainCategoryNo'], {
  unique: true,
  where: 'deleted = false',
})
@Index(DBIndexes.IDX_MAIN_CAUSE_ANALYSIS_MASTER_ID_MAIN_CATEGORY_NAME, ['causeAnalysisMasterId', 'mainCategoryName'], {
  unique: true,
  where: 'deleted = false',
})
export class CauseMainCategory extends IdentifyEntity {
  @Column()
  public mainCategoryNo: string;

  @Column()
  public mainCategoryName: string;

  @Column({ type: 'enum', enum: RiskTypeEnum })
  public potentialRisk: string;

  @Column({ type: 'enum', enum: MainSubEnum, default: MainSubEnum.PUBLISHED })
  public status: string;

  @Column()
  public causeAnalysisMasterId: string;

  @Column({ type: 'uuid' })
  public companyId: string;

  @ManyToOne(() => CauseAnalysisMaster, (causeAnalysisMaster) => causeAnalysisMaster.causeMainCategories, { onDelete: 'CASCADE' })
  causeAnalysisMaster: CauseAnalysisMaster;

  @OneToMany(() => CauseSubCategory, (causeSubCategory) => causeSubCategory.causeMainCategory, {
    onDelete: 'CASCADE',
  })
  causeSubCategories: CauseSubCategory[];

  @ManyToOne(() => Company, { onDelete: 'CASCADE' })
  company: Company;
}
