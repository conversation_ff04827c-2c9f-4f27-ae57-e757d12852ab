import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsNotEmpty,
  IsNumberString,
  IsOptional,
  IsUUID,
  MaxLength,
  ValidateNested,
  IsInt,
  Min,
  Max,
} from 'class-validator';

export class CreateCauseSubCategoryDto {
  @ApiProperty({ type: 'string', example: 'Sub Category Name' })
  @IsNotEmpty()
  @MaxLength(100)
  subCategoryName: string;

  @ApiProperty({ type: 'string', example: '1' })
  @IsNotEmpty()
  @IsNumberString()
  @MaxLength(20)
  subRefNo: string;

  @ApiProperty({ type: 'number', example: 1, description: 'Level: 1 for sub category, 2 for second sub category' })
  @IsInt()
  @Min(1)
  @Max(2)
  level: number;

  @IsOptional()
  @IsUUID('all')
  parentId?: string;

  @ValidateNested({ each: true })
  @Type(() => CreateCauseSubCategoryDto)
  @IsOptional()
  @ApiProperty({
    type: [CreateCauseSubCategoryDto],
    description: 'Second sub categories (children)',
    required: false,
  })
  children?: CreateCauseSubCategoryDto[];

  @IsOptional()
  @IsUUID('all')
  id?: string;
}
