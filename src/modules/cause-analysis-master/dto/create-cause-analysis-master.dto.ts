import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsEnum, IsNotEmpty, IsOptional, MaxLength, ValidateNested } from 'class-validator';
import { CommonStatus, IsMomentTimeZoneName } from 'svm-nest-lib-v3';
import { CauseTypeEnum } from '../../../commons/enums';
import { CreateCauseMainCategoryDto } from './create-cause-main-category.dto';

export class CreateCauseAnalysisMasterDto {
  @ApiProperty({ enum: CauseTypeEnum })
  @IsNotEmpty()
  @IsEnum(CauseTypeEnum)
  causeType: string;

  @ApiProperty({ enum: CommonStatus })
  @IsEnum(CommonStatus)
  status: string;

  @ApiProperty({ type: 'string', example: 'CAUSESVMSG202100001' })
  @IsNotEmpty()
  @MaxLength(20)
  udfVersionNo: string;

  @ApiProperty({ type: 'string', required: false })
  @IsOptional()
  @IsMomentTimeZoneName()
  timezone?: string;

  @IsNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => CreateCauseMainCategoryDto)
  @ApiProperty({
    type: [CreateCauseMainCategoryDto],
    description: 'Cause main categories',
    required: true,
  })
  causeMainCategories: CreateCauseMainCategoryDto[];
}
