import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsNotEmpty,
  IsNumberString,
  IsOptional,
  IsUUID,
  MaxLength,
  ValidateNested,
  IsEnum,
} from 'class-validator';
import { RiskTypeEnum } from '../../../commons/enums';
import { CreateCauseSubCategoryDto } from './create-cause-sub-category.dto';

export class CreateCauseMainCategoryDto {
  @ApiProperty({ type: 'string', example: '1' })
  @IsNotEmpty()
  @IsNumberString()
  @MaxLength(20)
  mainCategoryNo: string;

  @ApiProperty({ type: 'string', example: 'Main Category Name' })
  @IsNotEmpty()
  @MaxLength(100)
  mainCategoryName: string;

  @ApiProperty({ enum: RiskTypeEnum, example: RiskTypeEnum.HIGH })
  @IsNotEmpty()
  @IsEnum(RiskTypeEnum)
  potentialRisk: string;

  @ValidateNested({ each: true })
  @Type(() => CreateCauseSubCategoryDto)
  @IsOptional()
  @ApiProperty({
    type: [CreateCauseSubCategoryDto],
    description: 'Cause sub categories',
    required: false,
  })
  causeSubCategories?: CreateCauseSubCategoryDto[];

  @IsOptional()
  @IsUUID('all')
  id?: string;
}
