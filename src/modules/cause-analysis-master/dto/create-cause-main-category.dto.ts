import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsNotEmpty,
  IsNumberString,
  IsOptional,
  IsUUID,
  MaxLength,
  ValidateNested,
} from 'class-validator';
import { CreateCauseSubCategoryDto } from './create-cause-sub-category.dto';

export class CreateCauseMainCategoryDto {
  @ApiProperty({ type: 'string', example: '1' })
  @IsNotEmpty()
  @IsNumberString()
  @MaxLength(20)
  mainCategoryNo: string;

  @ApiProperty({ type: 'string', example: 'Main Category Name' })
  @IsNotEmpty()
  @MaxLength(100)
  mainCategoryName: string;

  @ApiProperty({
    type: 'string',
    example: 'uuid-of-priority-master-record',
    description: 'Priority Master ID for potential risk',
  })
  @IsNotEmpty()
  @IsUUID('all')
  potentialRiskId: string;

  @ValidateNested({ each: true })
  @Type(() => CreateCauseSubCategoryDto)
  @IsOptional()
  @ApiProperty({
    type: [CreateCauseSubCategoryDto],
    description: 'Cause sub categories',
    required: false,
  })
  causeSubCategories?: CreateCauseSubCategoryDto[];

  @IsOptional()
  @IsUUID('all')
  id?: string;
}
