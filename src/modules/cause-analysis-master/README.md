# Cause Analysis Master Module

## Overview

The Cause Analysis Master module provides a comprehensive system for managing hierarchical cause analysis data structures. It supports 3-level hierarchies (Master → Main Category → Sub Category → Second Sub Category) with configurable risk levels and business rule enforcement.

## Features

- **4 Cause Types**: Basic, Immediate, Control Action Needs, Type of Loss
- **3-Level Hierarchy**: Master → Main Category → Sub Category → Second Sub Category
- **Risk Management**: Integration with Priority Master for risk level assessment
- **Active Record Control**: Only one active record per cause type (except Type of Loss)
- **Company Scoping**: Multi-tenant data isolation
- **Full CRUD Operations**: Create, Read, Update, Delete with soft delete support
- **Advanced Filtering**: Search, pagination, and filtering capabilities

## Architecture

### Module Structure
```
src/modules/cause-analysis-master/
├── entities/
│   ├── cause-analysis-master.entity.ts
│   ├── cause-main-category.entity.ts
│   └── cause-sub-category.entity.ts
├── repositories/
│   ├── cause-analysis-master.repository.ts
│   ├── cause-main-category.repository.ts
│   └── cause-sub-category.repository.ts
├── dto/
│   ├── create-cause-analysis-master.dto.ts
│   ├── update-cause-analysis-master.dto.ts
│   ├── list-cause-analysis-master.dto.ts
│   ├── create-cause-main-category.dto.ts
│   ├── create-cause-sub-category.dto.ts
│   └── index.ts
├── cause-analysis-master.controller.ts
├── cause-analysis-master.service.ts
├── cause-analysis-master.module.ts
├── MIGRATION_README.md
├── UPDATED_CURL_EXAMPLES.md
├── test-migration.sql
└── README.md
```

### Data Model

#### Entity Relationships
```
CauseAnalysisMaster (1)
├── Company (N:1)
├── User (createdUser) (N:1)
├── User (updatedUser) (N:1)
└── CauseMainCategory (1:N)
    ├── PriorityMaster (potentialRisk) (N:1)
    └── CauseSubCategory (1:N)
        └── CauseSubCategory (children) (1:N)
```

#### Hierarchy Structure
```
Cause Analysis Master
├── Main Category 1
│   ├── Sub Category 1.1 (Level 1)
│   │   ├── Second Sub Category 1.1.1 (Level 2)
│   │   └── Second Sub Category 1.1.2 (Level 2)
│   └── Sub Category 1.2 (Level 1)
└── Main Category 2
    └── Sub Category 2.1 (Level 1)
```

## Installation

### 1. Database Migration

Run the migration to create the required tables:

```bash
# Development
npm run migration:run

# Production
npm run migration:run:prod
```

### 2. Module Registration

The module is automatically registered in `app.module.ts`:

```typescript
import { CauseAnalysisMasterModule } from '../cause-analysis-master/cause-analysis-master.module';

@Module({
  imports: [
    // ... other modules
    CauseAnalysisMasterModule,
  ],
})
export class AppModule {}
```

### 3. Prerequisites

Ensure the following tables exist and have data:
- `company` - Company records
- `user` - User records  
- `priority_master` - Risk level data (High, Medium, Low, Negligible)

## API Endpoints

### Base URL
```
/cause-analysis-master
```

### Endpoints

| Method | Endpoint | Description | Permissions |
|--------|----------|-------------|-------------|
| POST | `/` | Create new cause analysis master | Admin, CREATE |
| GET | `/` | List cause analysis masters | Admin/User, VIEW |
| GET | `/:id` | Get cause analysis master detail | Admin/User, VIEW |
| DELETE | `/:id` | Delete cause analysis master | Admin, DELETE |

### Request/Response Examples

#### Create Cause Analysis Master
```bash
POST /cause-analysis-master
Content-Type: application/json
Authorization: Bearer <token>

{
  "causeType": "Basic",
  "status": "active",
  "udfVersionNo": "BASIC_V1.0",
  "timezone": "Asia/Ho_Chi_Minh",
  "causeMainCategories": [
    {
      "mainCategoryNo": "1",
      "mainCategoryName": "Human Error",
      "potentialRiskId": "uuid-of-high-risk-priority",
      "causeSubCategories": [
        {
          "subCategoryName": "Training Deficiency",
          "subRefNo": "1.1",
          "level": 1,
          "children": [
            {
              "subCategoryName": "Inadequate Initial Training",
              "subRefNo": "1.1.1",
              "level": 2
            }
          ]
        }
      ]
    }
  ]
}
```

#### List with Filtering
```bash
GET /cause-analysis-master?causeType=Basic&status=active&page=1&pageSize=10&search=V1
```

#### Detail Response
```json
{
  "id": "uuid",
  "causeType": "Basic",
  "udfVersionNo": "BASIC_V1.0",
  "refNo": "CAUSESVMSG202400001",
  "status": "active",
  "causeMainCategories": [
    {
      "id": "uuid",
      "mainCategoryNo": "1",
      "mainCategoryName": "Human Error",
      "potentialRisk": {
        "id": "uuid",
        "risk": "High",
        "order": 1
      },
      "causeSubCategories": [
        {
          "id": "uuid",
          "subCategoryName": "Training Deficiency",
          "subRefNo": "1.1",
          "level": 1,
          "children": [
            {
              "id": "uuid",
              "subCategoryName": "Inadequate Initial Training",
              "subRefNo": "1.1.1",
              "level": 2
            }
          ]
        }
      ]
    }
  ]
}
```

## Business Rules

### 1. Active Record Constraints
- Only **one active record** per cause type per company
- Exception: **Type of Loss** can have multiple active records
- Creating a new active record automatically deactivates previous ones

### 2. Hierarchy Constraints
- Maximum **2 levels** of sub-categories
- Level 1: `parentId` IS NULL, `level` = 1
- Level 2: `parentId` IS NOT NULL, `level` = 2

### 3. Unique Constraints
- Version numbers must be unique per company
- Category numbers and names must be unique within each master
- Sub-category reference numbers must be unique within each level

### 4. Reference Number Generation
- System generates unique reference numbers: `CAUSE{CompanyCode}{Year}{Counter}`
- Example: `CAUSESVMSG202400001`

## Configuration

### Cause Types
```typescript
enum CauseTypeEnum {
  BASIC = 'Basic',
  IMMEDIATE = 'Immediate',
  CONTROL_ACTION_NEEDS = 'Control Action Needs',
  TYPE_OF_LOSS = 'Type of Loss',
}
```

### Risk Levels (from Priority Master)
- **High** (Order: 1)
- **Medium** (Order: 2)  
- **Low** (Order: 3)
- **Negligible** (Order: 4)

## Usage Examples

### Getting Priority Master IDs
```bash
# First, get available risk levels
curl -X GET "http://localhost:3000/priority-master" \
  -H "Authorization: Bearer <token>"

# Response:
[
  {"id": "uuid-1", "risk": "High", "order": 1},
  {"id": "uuid-2", "risk": "Medium", "order": 2},
  {"id": "uuid-3", "risk": "Low", "order": 3},
  {"id": "uuid-4", "risk": "Negligible", "order": 4}
]
```

### Creating Different Cause Types
```bash
# Basic Cause
curl -X POST "http://localhost:3000/cause-analysis-master" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{"causeType": "Basic", "status": "active", "udfVersionNo": "BASIC_V1.0", ...}'

# Immediate Cause  
curl -X POST "http://localhost:3000/cause-analysis-master" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{"causeType": "Immediate", "status": "active", "udfVersionNo": "IMMEDIATE_V1.0", ...}'
```

## Testing

### Unit Tests
```bash
# Run cause analysis master tests
npm test -- --testPathPattern=cause-analysis-master

# Run with coverage
npm run test:cov -- --testPathPattern=cause-analysis-master
```

### Migration Testing
```bash
# Test migration
psql -d your_database -f src/modules/cause-analysis-master/test-migration.sql
```

### API Testing
See `UPDATED_CURL_EXAMPLES.md` for comprehensive API testing examples.

## Troubleshooting

### Common Issues

1. **Foreign Key Violations**
   - Ensure `priority_master` table has data
   - Verify `potentialRiskId` exists in priority_master

2. **Unique Constraint Violations**
   - Check for duplicate version numbers
   - Verify category numbers/names are unique within master

3. **Active Record Logic**
   - Only one active record per cause type (except Type of Loss)
   - Previous active records are automatically deactivated

### Error Messages
- `causeAnalysisMaster.NOT_FOUND` - Record not found
- `potentialRiskId must be a UUID` - Invalid risk ID format
- `Version number already exists` - Duplicate version number

## Performance Considerations

### Indexes
- Company-based filtering: `idx_cause_analysis_master_company_id`
- Cause type filtering: `idx_cause_analysis_master_cause_type`
- Risk level filtering: `idx_cause_main_category_potential_risk_id`
- Hierarchy navigation: `idx_cause_sub_category_parent_id`

### Query Optimization
- Use pagination for large datasets
- Filter by company and cause type for better performance
- Leverage indexes for search operations

## Security

### Permissions Required
- **CREATE**: `QUALITY_ASSURANCE_INCIDENTS::CAUSE_ANALYSIS_MASTER::CREATE`
- **VIEW**: `QUALITY_ASSURANCE_INCIDENTS::CAUSE_ANALYSIS_MASTER::VIEW`
- **DELETE**: `QUALITY_ASSURANCE_INCIDENTS::CAUSE_ANALYSIS_MASTER::DELETE`

### Data Isolation
- All data is scoped to user's company
- Cross-company data access is prevented
- Soft delete maintains audit trail

## Migration Guide

For detailed migration instructions, see:
- `MIGRATION_README.md` - Complete migration guide
- `test-migration.sql` - Migration verification script
- `UPDATED_CURL_EXAMPLES.md` - Updated API examples

## Contributing

1. Follow the established entity patterns
2. Maintain repository pattern for database operations
3. Include comprehensive unit tests
4. Update documentation for any changes
5. Follow the existing code style and conventions

## Development

### Adding New Features

When extending the module:

1. **New Cause Types**: Add to `CauseTypeEnum` and update validation
2. **Additional Fields**: Follow entity patterns and update DTOs
3. **New Relationships**: Maintain foreign key constraints
4. **Business Rules**: Implement in service layer, not database

### Code Standards

```typescript
// Entity Example
@Entity()
export class NewEntity extends IdentifyEntity {
  @Column()
  public field: string;

  @ManyToOne(() => RelatedEntity)
  relation: RelatedEntity;
}

// Service Example
async createEntity(dto: CreateDto, user: TokenPayloadModel) {
  // Validation
  // Business logic
  // Repository call
  return result;
}
```

### Testing Patterns

```typescript
describe('CauseAnalysisMasterService', () => {
  beforeEach(async () => {
    // Setup test module
  });

  it('should create cause analysis master', async () => {
    // Arrange
    // Act
    // Assert
  });
});
```

## Monitoring

### Key Metrics
- **Creation Rate**: New cause analysis masters per day
- **Active Records**: Number of active masters per cause type
- **Hierarchy Depth**: Average depth of category structures
- **API Performance**: Response times for list/detail endpoints

### Logging
- All CRUD operations are logged
- Business rule violations are tracked
- Performance metrics are captured

## Backup and Recovery

### Data Backup
```sql
-- Backup cause analysis data
pg_dump -t cause_analysis_master -t cause_main_category -t cause_sub_category your_database > cause_analysis_backup.sql
```

### Data Recovery
```sql
-- Restore from backup
psql your_database < cause_analysis_backup.sql
```

## Version History

### v1.0.0 (Current)
- Initial implementation with 3-level hierarchy
- Priority Master integration for risk levels
- Full CRUD operations with business rules
- Company-scoped data isolation
- Active record management

### Planned Features
- **v1.1.0**: Bulk import/export functionality
- **v1.2.0**: Advanced reporting and analytics
- **v1.3.0**: Integration with incident management
- **v2.0.0**: Enhanced hierarchy with unlimited levels

## Related Modules

### Dependencies
- **Priority Master**: Risk level management
- **Company**: Multi-tenant data scoping
- **User**: Audit trail and permissions
- **Commons**: Shared enums and utilities

### Integration Points
- **Incident Module**: Cause analysis linkage
- **Audit Module**: Finding categorization
- **Reporting**: Analytics and dashboards

## FAQ

### Q: Can I have more than 2 levels of sub-categories?
A: Currently limited to 2 levels. This can be extended in future versions.

### Q: Why use Priority Master instead of enum for risk levels?
A: Provides flexibility, referential integrity, and consistency with other modules.

### Q: Can Type of Loss have multiple active records?
A: Yes, Type of Loss is exempt from the single active record rule.

### Q: How are reference numbers generated?
A: System generates: `CAUSE{CompanyCode}{Year}{5-digit-counter}`

### Q: Can I modify existing cause analysis masters?
A: Currently supports soft delete only. Update functionality planned for future versions.

## Support

For issues or questions:
1. Check the troubleshooting section
2. Review the migration documentation
3. Test with the provided cURL examples
4. Check the FAQ section
5. Contact the development team

## License

This module is part of the SVM Assets application and follows the project's licensing terms.
